// City interface for API responses
export interface City {
   id: string;
   name: string;
   countryId: string;
   createdAt: string;
   updatedAt: string;
   deletedAt: string | null;
}

// Driver registration and OTP types
export interface DriverRegistrationRequest {
   phoneNumber: string;
}

export interface DriverRegistrationResponse {
   success: boolean;
   message: string;
   data: {
      userId: string;
      phoneNumber: string;
      message: string;
   };
   timestamp: number;
}

export interface DriverOtpVerificationRequest {
   phoneNumber: string;
   otp: string;
}

export interface DriverResendOtpRequest {
   phoneNumber: string;
}

// Updated Driver interface to match actual API response structure
export interface Driver {
   id: string;
   userId: string;
   roleId: string;
   firstName: string | null;
   lastName: string | null;
   email: string | null;
   phoneNumber: string; // API uses phoneNumber, not mobileNumber
   gender: 'MALE' | 'FEMALE' | 'OTHER' | null;
   dob: string | null;
   profilePictureUrl: string | null;
   cityId: string | null;
   cityName?: string; // API includes cityName for display
   referralCode: string | null;
   phoneVerified: boolean;
   emailVerified: boolean;
   createdAt: string;
   updatedAt: string;
}

// API response structure for listing drivers
export interface ListDriverResponse {
   success: boolean;
   message: string;
   data: Driver[];
   meta?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
   };
   timestamp: number;
}

// API response structure for single driver
export interface DriverResponse {
   success: boolean;
   message: string;
   data: Driver;
   timestamp: number;
}

// Request for creating driver profile (after OTP verification)
export interface CreateDriverRequest {
   userId: string; // Required field
   firstName: string; // Required field
   lastName: string; // Required field
   mobileNumber: string; // Required field - API uses mobileNumber, not phoneNumber
   email: string; // Required field
   gender: 'MALE' | 'FEMALE' | 'OTHER'; // Required field
   dob: string; // Required field - ISO 8601 date string
   profilePictureUrl?: string; // Optional field
   cityId: string; // Required field - UUID
}

// Request for updating driver profile
export interface UpdateDriverRequest {
   firstName?: string;
   lastName?: string;
   gender?: 'MALE' | 'FEMALE' | 'OTHER';
   dob?: string;
   profilePictureUrl?: string;
   cityId?: string;
}

// Parameters for listing drivers with filters
export interface ListDriverParams {
   page?: number;
   limit?: number;
   sortBy?: string;
   sortOrder?: 'asc' | 'desc';
   search?: string;
   cityId?: string;
   name?: string;
   email?: string;
   phoneNumber?: string;
}

export interface DriverFilters {
   page?: number;
   perPage?: number;
   search?: string;
   status?: 'pending' | 'active' | 'inactive';
   location?: string;
}
